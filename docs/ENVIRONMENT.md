# Environment Configuration

This document describes the environment configuration system for the zScore Agent application.

## Overview

The application uses a modular environment configuration system that supports different settings for development, production, and local overrides.

## Environment Files

### `.env.development`
Used during development (`npm run dev`). Contains development-specific settings:
- External API URL: `http://52.66.192.54:8001`
- Debug mode enabled
- Verbose logging

### `.env.production`
Used in production builds (`npm run build && npm start`). Contains production-specific settings:
- External API URL: `https://agentapi.myzscore.ai`
- Debug mode disabled
- Error-level logging only

### `.env.local`
Local overrides for development. This file is:
- Not committed to version control
- Loaded in all environments
- Used for local-specific configuration

## Configuration Module

The environment configuration is managed by `src/config/environment.ts`, which provides:

- Type-safe environment variable access
- Validation of required variables
- Environment-specific defaults
- Centralized configuration management

## Available Variables

### API Configuration
- `EXTERNAL_API_URL`: External API base URL
- `EXTERNAL_API_ENDPOINT`: API endpoint path (default: `/query`)
- `EXTERNAL_API_TIMEOUT`: Request timeout in milliseconds (default: 30000)

### Application Configuration
- `APP_NAME`: Application name (default: "zScore Agent")
- `APP_VERSION`: Application version (default: "1.0.0")
- `NODE_ENV`: Environment mode (development/production)

### Debug Configuration
- `DEBUG_MODE`: Enable debug logging (boolean)
- `LOG_LEVEL`: Logging level (debug/info/warn/error)

## Usage

### Development
```bash
npm run dev
```
Loads: `.env.development` + `.env.local`

### Production
```bash
npm run build
npm start
```
Loads: `.env.production` + `.env.local`

### Local Overrides
Create or edit `.env.local` to override any environment variable:
```env
# Override API URL for local testing
EXTERNAL_API_URL=http://localhost:8001

# Enable debug mode in production
DEBUG_MODE=true
```

## API Endpoints

### Health Check
```bash
curl http://localhost:3000/api/health
```

Returns environment information:
```json
{
  "status": "ok",
  "timestamp": "2025-07-09T21:49:31.376Z",
  "service": "zScore Agent",
  "version": "1.0.0",
  "environment": "development",
  "api": {
    "configured": true,
    "timeout": 30000
  }
}
```

### Query API
```bash
curl -X POST http://localhost:3000/api/query \
  -H "Content-Type: application/json" \
  -d '{"prompt": "What is my zScore?"}'
```

## Security

- Environment files containing sensitive data are excluded from version control
- Production URLs are only used in production environment
- Debug information is only logged in development mode
- API URLs are not exposed to the frontend

## Validation

The configuration system validates:
- Required environment variables are present
- API URLs are valid
- Timeout values are positive numbers
- Environment-specific constraints

## Troubleshooting

### Missing Environment Variables
If required variables are missing, the application will:
- Log an error message
- Exit in production mode
- Continue with warnings in development mode

### Invalid Configuration
Check the console for validation errors:
```
Environment configuration error: Invalid EXTERNAL_API_URL format: invalid-url
```

### Testing Configuration
Run the test script to verify environment setup:
```bash
node scripts/test-env.js
```
