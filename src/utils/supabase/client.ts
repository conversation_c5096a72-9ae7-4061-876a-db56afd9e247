import { createBrowserClient } from '@supabase/ssr'

export function createClient() {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  // Check if Supabase is properly configured
  if (!supabaseUrl || !supabaseAnonKey ||
      supabaseUrl.includes('your_supabase_project_url') ||
      supabaseAnonKey.includes('your_supabase_anon_key')) {
    return null; // Return null instead of throwing error
  }

  try {
    // Create a supabase client on the browser with project's credentials
    return createBrowserClient(supabaseUrl, supabaseAnonKey)
  } catch (error) {
    console.warn('Failed to create Supabase client:', error);
    return null;
  }
}

export function isSupabaseConfigured(): boolean {
  const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
  const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

  return !!(supabaseUrl && supabaseAnonKey &&
           !supabaseUrl.includes('your_supabase_project_url') &&
           !supabaseAnonKey.includes('your_supabase_anon_key'));
}
