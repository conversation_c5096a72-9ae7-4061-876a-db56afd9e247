/**
 * Dynamic Theme Utility for myZScore.ai
 * 
 * This utility provides a robust theming system that automatically adapts
 * to theme changes without requiring manual dark/light mode adjustments.
 * 
 * All colors and styles use CSS custom properties that automatically
 * update when the theme changes.
 */

export const themeClasses = {
  // Text Colors - Auto-adapting
  text: {
    primary: 'text-text-primary',
    secondary: 'text-text-secondary', 
    tertiary: 'text-text-tertiary',
    brand: 'text-primary-500',
    white: 'text-white', // For use on colored backgrounds
  },

  // Background Colors - Auto-adapting
  bg: {
    primary: 'bg-bg-primary',
    secondary: 'bg-bg-secondary',
    tertiary: 'bg-bg-tertiary',
    surface: 'bg-surface',
    surfaceElevated: 'bg-surface-elevated',
    gradientPrimary: 'bg-gradient-primary',
    gradientMulti: 'bg-gradient-multi',
    gradientSubtle: 'bg-gradient-subtle',
    gradientMesh: 'bg-gradient-mesh',
  },

  // Border Colors - Auto-adapting
  border: {
    primary: 'border-border-primary',
    secondary: 'border-border-secondary',
    brand: 'border-primary-500',
  },

  // Accent Colors - Auto-adapting
  accent: {
    cyan: 'text-accent-cyan bg-accent-cyan',
    pink: 'text-accent-pink bg-accent-pink',
    green: 'text-accent-green bg-accent-green',
    orange: 'text-accent-orange bg-accent-orange',
  },

  // Icon Colors - Auto-adapting
  icon: {
    primary: 'text-primary-500',
    secondary: 'text-text-secondary',
    tertiary: 'text-text-tertiary',
    white: 'text-white', // For use on colored backgrounds
    brand: 'text-primary-500',
    interactive: 'text-text-secondary hover:text-text-primary',
  },

  // Interactive States - Auto-adapting
  interactive: {
    card: 'interactive-card hover:border-primary-500/30',
    button: 'interactive-button',
    buttonPrimary: 'interactive-button bg-gradient-primary text-white',
    focus: 'focus-smooth',
  },

  // Typography - Auto-adapting with brand fonts
  typography: {
    displayLarge: 'text-display-large font-space-grotesk',
    displayMedium: 'text-display-medium font-space-grotesk',
    heading1: 'text-heading-1 font-space-grotesk',
    heading2: 'text-heading-2 font-space-grotesk',
    heading3: 'text-heading-3 font-space-grotesk',
    bodyLarge: 'text-body-large font-inter',
    bodyRegular: 'text-body-regular font-inter',
    bodySmall: 'text-body-small font-inter',
    button: 'text-button font-space-grotesk',
  },

  // Shadows - Auto-adapting
  shadow: {
    soft: 'shadow-soft',
    medium: 'shadow-medium',
    large: 'shadow-large',
    brand: 'shadow-brand',
    brandLg: 'shadow-brand-lg',
    glow: 'shadow-glow',
    glowLg: 'shadow-glow-lg',
  },

  // Animations - Theme-aware
  animation: {
    fadeIn: 'animate-fade-in',
    slideUp: 'animate-slide-up',
    slideDown: 'animate-slide-down',
    scaleIn: 'animate-scale-in',
    pulseSoft: 'animate-pulse-soft',
    gentlePulse: 'animate-gentle-pulse',
    subtleFloat: 'animate-subtle-float',
    smoothSlideIn: 'animate-smooth-slide-in',
    smoothFadeIn: 'animate-smooth-fade-in',
    buttonPress: 'animate-button-press',
  },

  // Layout - Auto-adapting
  layout: {
    cardProfessional: 'card-professional',
    inputProfessional: 'input-professional',
    roundedBrand: 'rounded-brand',
    roundedBrandLg: 'rounded-brand-lg',
  },
} as const;

/**
 * Utility function to combine theme classes
 * @param classes - Array of class strings or theme class keys
 * @returns Combined class string
 */
export function cn(...classes: (string | undefined | null | false)[]): string {
  return classes.filter(Boolean).join(' ');
}

/**
 * Get brand-specific gradient text classes that work in all themes
 * @returns Class string for gradient text
 */
export function getBrandGradientText(): string {
  return 'bg-gradient-primary bg-clip-text text-transparent font-space-grotesk font-semibold';
}

/**
 * Get brand-specific primary color that adapts to theme
 * @returns Class string for brand primary color
 */
export function getBrandPrimary(): string {
  return 'bg-gradient-primary bg-clip-text text-transparent font-space-grotesk font-semibold';
}

/**
 * Theme-aware component classes for common UI patterns
 */
export const componentClasses = {
  // Header components
  header: {
    container: cn(
      themeClasses.bg.surface,
      themeClasses.border.primary,
      'border-b backdrop-blur-xl'
    ),
    title: cn(
      themeClasses.typography.heading3,
      themeClasses.text.brand
    ),
    subtitle: cn(
      themeClasses.typography.bodySmall,
      themeClasses.text.secondary
    ),
  },

  // Welcome screen components
  welcome: {
    container: 'h-full flex flex-col items-center justify-center max-w-5xl mx-auto px-6 relative pb-32',
    title: cn(
      themeClasses.typography.heading2,
      themeClasses.text.primary,
      'mb-4 text-center leading-tight'
    ),
    subtitle: cn(
      themeClasses.typography.bodyLarge,
      themeClasses.text.secondary,
      'max-w-lg mx-auto mb-3 text-center leading-relaxed'
    ),
    brandText: getBrandPrimary(),
  },

  // Button components
  button: {
    primary: cn(
      themeClasses.interactive.buttonPrimary,
      themeClasses.typography.button,
      'px-4 py-2 rounded-xl group'
    ),
    secondary: cn(
      themeClasses.interactive.card,
      themeClasses.bg.surface,
      themeClasses.border.primary,
      'p-3 rounded-xl group'
    ),
  },

  // Card components
  card: {
    professional: cn(
      themeClasses.layout.cardProfessional,
      themeClasses.interactive.card,
      'hover:border-primary-500/30'
    ),
    suggestion: cn(
      themeClasses.layout.cardProfessional,
      themeClasses.interactive.card,
      'text-left hover:border-primary-500/30'
    ),
  },

  // Input components
  input: {
    container: cn(
      themeClasses.bg.surface,
      themeClasses.border.primary,
      'flex gap-3 p-4 rounded-2xl border shadow-soft focus-within:shadow-medium focus-within:border-primary-500/50 transition-all duration-300'
    ),
    field: cn(
      themeClasses.layout.inputProfessional,
      themeClasses.interactive.focus,
      'flex-1 py-1'
    ),
  },

  // Message components
  message: {
    user: cn(
      themeClasses.bg.gradientPrimary,
      'text-white px-4 py-3 rounded-2xl shadow-soft'
    ),
    ai: cn(
      themeClasses.bg.surface,
      themeClasses.border.primary,
      'border px-4 py-3 rounded-2xl shadow-soft hover:shadow-medium'
    ),
    avatar: {
      ai: cn(
        themeClasses.bg.gradientPrimary,
        'w-8 h-8 rounded-lg flex items-center justify-center shadow-soft'
      ),
      user: cn(
        'bg-accent-cyan w-8 h-8 rounded-lg flex items-center justify-center shadow-soft'
      ),
    },
  },
} as const;

/**
 * Theme detection utility
 * @returns Current theme ('light' | 'dark')
 */
export function getCurrentTheme(): 'light' | 'dark' {
  if (typeof window === 'undefined') return 'light';
  return document.documentElement.classList.contains('dark') ? 'dark' : 'light';
}

/**
 * Toggle theme utility
 * @param currentTheme - Current theme state
 * @returns New theme state
 */
export function toggleTheme(currentTheme: 'light' | 'dark'): 'light' | 'dark' {
  const newTheme = currentTheme === 'dark' ? 'light' : 'dark';
  document.documentElement.classList.toggle('dark', newTheme === 'dark');
  return newTheme;
}
