'use client';

import { motion } from 'framer-motion';
import { AlertCircle, ArrowLeft } from 'lucide-react';
import Link from 'next/link';
import { componentClasses } from '@/utils/theme';

export default function ErrorPage() {
  return (
    <div className="min-h-screen flex items-center justify-center relative overflow-hidden">
      {/* Background */}
      <div className="absolute inset-0 bg-gradient-to-br from-bg-primary via-bg-secondary to-bg-tertiary"></div>
      
      {/* Error Content */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6, ease: "easeOut" }}
        className="relative z-10 w-full max-w-md mx-auto px-6 text-center"
      >
        <div className="bg-surface/95 backdrop-blur-xl border border-border-primary rounded-2xl shadow-large p-8">
          {/* Error Icon */}
          <motion.div
            initial={{ opacity: 0, scale: 0.8 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ duration: 0.5, delay: 0.2 }}
            className="w-16 h-16 mx-auto bg-red-500/10 border border-red-500/20 rounded-2xl flex items-center justify-center mb-6"
          >
            <AlertCircle className="text-red-500" size={28} />
          </motion.div>
          
          {/* Error Message */}
          <motion.h1
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            className="text-2xl font-bold font-space-grotesk mb-4"
            style={{ color: 'var(--text-primary)' }}
          >
            Authentication Error
          </motion.h1>
          
          <motion.p
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.4 }}
            className="text-sm mb-8"
            style={{ color: 'var(--text-secondary)' }}
          >
            Something went wrong during authentication. Please try again or contact support if the problem persists.
          </motion.p>

          {/* Back to Login Button */}
          <motion.div
            initial={{ opacity: 0, y: 10 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <Link
              href="/login"
              className="inline-flex items-center gap-2 py-3 px-6 bg-gradient-primary text-white rounded-xl font-medium hover:shadow-brand-lg transition-all duration-200"
            >
              <ArrowLeft size={18} />
              Back to Login
            </Link>
          </motion.div>
        </div>
      </motion.div>
    </div>
  );
}
