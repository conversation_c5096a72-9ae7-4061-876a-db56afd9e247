import { NextRequest, NextResponse } from 'next/server';
import { env } from '@/config/environment';

/**
 * Interface for the request body
 */
interface QueryRequestBody {
  prompt: string;
}

/**
 * Interface for the external API response
 */
interface ExternalApiResponse {
  result: string;
}

/**
 * POST handler for /api/query
 * Proxies requests to the external API while hiding the actual API URL
 */
export async function POST(request: NextRequest) {
  try {
    // Parse and validate request body
    const body: QueryRequestBody = await request.json();
    
    if (!body.prompt || typeof body.prompt !== 'string') {
      return NextResponse.json(
        { error: 'Invalid request: prompt is required and must be a string' },
        { status: 400 }
      );
    }

    if (!body.prompt.trim()) {
      return NextResponse.json(
        { error: 'Invalid request: prompt cannot be empty' },
        { status: 400 }
      );
    }

    // Create abort controller for timeout
    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), env.api.timeout);

    try {
      if (env.debug.enabled) {
        console.log('Proxying request to external API:', body.prompt);
        console.log('Using API URL:', env.api.fullUrl);
      }

      // Make request to external API
      const response = await fetch(env.api.fullUrl, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt: body.prompt.trim() }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (env.debug.enabled) {
        console.log('External API response status:', response.status);
      }

      if (!response.ok) {
        if (env.debug.enabled) {
          console.error('External API error:', response.status, response.statusText);
        }
        
        // Return a generic error to avoid exposing external API details
        return NextResponse.json(
          { error: 'External service temporarily unavailable' },
          { status: 503 }
        );
      }

      const data: ExternalApiResponse = await response.json();

      if (env.debug.enabled) {
        console.log('External API response received');
      }

      // Validate response format
      if (!data.result) {
        if (env.debug.enabled) {
          console.error('Invalid response format from external API');
        }
        return NextResponse.json(
          { error: 'Invalid response from external service' },
          { status: 502 }
        );
      }

      // Return the response in the expected format
      return NextResponse.json({ result: data.result });

    } catch (fetchError) {
      clearTimeout(timeoutId);
      
      if (fetchError instanceof Error) {
        if (env.debug.enabled) {
          console.error('External API request failed:', fetchError.message);
        }
        
        if (fetchError.name === 'AbortError') {
          return NextResponse.json(
            { error: 'Request timeout - please try again' },
            { status: 504 }
          );
        }
        
        if (fetchError.message.includes('Failed to fetch')) {
          return NextResponse.json(
            { error: 'Unable to connect to external service' },
            { status: 503 }
          );
        }
      }

      return NextResponse.json(
        { error: 'External service error' },
        { status: 503 }
      );
    }

  } catch (error) {
    if (env.debug.enabled) {
      console.error('API route error:', error);
    }
    
    if (error instanceof SyntaxError) {
      return NextResponse.json(
        { error: 'Invalid JSON in request body' },
        { status: 400 }
      );
    }

    return NextResponse.json(
      { error: 'Internal server error' },
      { status: 500 }
    );
  }
}

/**
 * GET handler for health check
 */
export async function GET() {
  return NextResponse.json({ status: 'ok', timestamp: new Date().toISOString() });
}
