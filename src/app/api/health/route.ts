import { NextResponse } from 'next/server';
import { env } from '@/config/environment';

/**
 * GET handler for /api/health
 * Health check endpoint with environment information
 */
export async function GET() {
  return NextResponse.json({
    status: 'ok',
    timestamp: new Date().toISOString(),
    service: env.app.name,
    version: env.app.version,
    environment: env.nodeEnv,
    api: {
      configured: !!env.api.externalUrl,
      timeout: env.api.timeout,
    }
  });
}
