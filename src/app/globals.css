@import url('https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600;700;800&family=Space+Grotesk:wght@400;500;600;700;800&display=swap');
@import "tailwindcss";

:root {
  /* Brand Colors - Light Theme (myZScore.ai Brand Kit) */
  --primary-500: #8B5CF6;
  --primary-400: #A78BFA;
  --primary-600: #7C3AED;
  --primary-300: #C4B5FD;

  /* Accent Colors (myZScore.ai Brand Kit) */
  --accent-cyan: #06B6D4;
  --accent-pink: #EC4899;
  --accent-green: #10B981;
  --accent-orange: #F59E0B;

  /* Light Theme Neutrals (myZScore.ai Brand Kit) */
  --bg-primary: #FFFFFF;
  --bg-secondary: #F3F4F6;
  --bg-tertiary: #F1F5F9;
  --surface: #F3F4F6;
  --surface-elevated: #FFFFFF;
  --text-primary: #000000;
  --text-secondary: #4B5563;
  --text-tertiary: #64748B;
  --border-primary: #D1D5DB;
  --border-secondary: #CBD5E1;

  /* Light Gradients (myZScore.ai Brand Kit) */
  --gradient-primary: linear-gradient(135deg, #8B5CF6 0%, #A78BFA 100%);
  --gradient-multi: linear-gradient(135deg, #8B5CF6 0%, #EC4899 50%, #06B6D4 100%);
  --gradient-subtle: linear-gradient(135deg, #F8FAFC 0%, #EDE9FE 100%);
  --gradient-mesh: radial-gradient(circle at 20% 80%, #8B5CF6 0%, transparent 50%),
                   radial-gradient(circle at 80% 20%, #EC4899 0%, transparent 50%),
                   radial-gradient(circle at 40% 40%, #06B6D4 0%, transparent 50%);

  /* Cloud-like Background Gradients */
  --gradient-cloud-bg: linear-gradient(135deg, #F8FAFC 0%, #EDE9FE 25%, #DDD6FE 50%, #C4B5FD 75%, #A78BFA 100%);
  --gradient-cloud-overlay: radial-gradient(ellipse 800px 600px at 20% 10%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
                           radial-gradient(ellipse 600px 400px at 80% 30%, rgba(167, 139, 250, 0.12) 0%, transparent 50%),
                           radial-gradient(ellipse 400px 300px at 40% 70%, rgba(196, 181, 253, 0.1) 0%, transparent 50%),
                           radial-gradient(ellipse 500px 350px at 70% 80%, rgba(221, 214, 254, 0.08) 0%, transparent 50%);

  /* Professional Spacing System */
  --spacing-xs: 0.25rem;    /* 4px */
  --spacing-sm: 0.5rem;     /* 8px */
  --spacing-md: 0.75rem;    /* 12px */
  --spacing-lg: 1rem;       /* 16px */
  --spacing-xl: 1.5rem;     /* 24px */
  --spacing-2xl: 2rem;      /* 32px */
  --spacing-3xl: 3rem;      /* 48px */
  --spacing-4xl: 4rem;      /* 64px */

  /* Professional Typography Scale (myZScore.ai Brand Kit) */
  --text-xs: 0.75rem;       /* 12px */
  --text-sm: 0.875rem;      /* 14px - Body Small */
  --text-base: 1rem;        /* 16px - Body Regular */
  --text-lg: 1.125rem;      /* 18px - Body Large */
  --text-xl: 1.25rem;       /* 20px */
  --text-2xl: 1.5rem;       /* 24px - Heading 3 */
  --text-3xl: 1.875rem;     /* 30px - Heading 2 */
  --text-4xl: 2.25rem;      /* 36px - Heading 1 */
  --text-5xl: 3rem;         /* 48px - Display Medium */
  --text-6xl: 4rem;         /* 64px - Display Large */

  /* Professional Line Heights */
  --leading-tight: 1.1;     /* Display Large */
  --leading-snug: 1.2;      /* Display Medium */
  --leading-normal: 1.3;    /* Headings */
  --leading-relaxed: 1.4;   /* Subsections */
  --leading-loose: 1.5;     /* Captions */
  --leading-body: 1.6;      /* Body text */
}

.dark {
  /* Dark Theme Colors (myZScore.ai Brand Kit) */
  --primary-500: #A78BFA;
  --primary-400: #C4B5FD;
  --primary-600: #8B5CF6;
  --primary-300: #DDD6FE;

  /* Accent Colors Dark (myZScore.ai Brand Kit) */
  --accent-cyan: #22D3EE;
  --accent-pink: #F472B6;
  --accent-green: #34D399;
  --accent-orange: #FBBF24;

  /* Dark Theme Neutrals (myZScore.ai Brand Kit) */
  --bg-primary: #111827;
  --bg-secondary: #1F2937;
  --bg-tertiary: #334155;
  --surface: #1F2937;
  --surface-elevated: #334155;
  --text-primary: #FFFFFF;
  --text-secondary: #9CA3AF;
  --text-tertiary: #94A3B8;
  --border-primary: #4B5563;
  --border-secondary: #475569;

  /* Dark Gradients */
  --gradient-primary: linear-gradient(135deg, #A78BFA 0%, #C4B5FD 100%);
  --gradient-multi: linear-gradient(135deg, #A78BFA 0%, #F472B6 50%, #22D3EE 100%);
  --gradient-subtle: linear-gradient(135deg, #1E293B 0%, #312E81 100%);
  --gradient-mesh: radial-gradient(circle at 20% 80%, #A78BFA 0%, transparent 50%),
                   radial-gradient(circle at 80% 20%, #F472B6 0%, transparent 50%),
                   radial-gradient(circle at 40% 40%, #22D3EE 0%, transparent 50%);

  /* Dark Cloud-like Background Gradients */
  --gradient-cloud-bg: linear-gradient(135deg, #0F172A 0%, #1E293B 25%, #312E81 50%, #4C1D95 75%, #5B21B6 100%);
  --gradient-cloud-overlay: radial-gradient(ellipse 800px 600px at 20% 10%, rgba(167, 139, 250, 0.2) 0%, transparent 50%),
                           radial-gradient(ellipse 600px 400px at 80% 30%, rgba(196, 181, 253, 0.15) 0%, transparent 50%),
                           radial-gradient(ellipse 400px 300px at 40% 70%, rgba(221, 214, 254, 0.12) 0%, transparent 50%),
                           radial-gradient(ellipse 500px 350px at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%);
}

@theme inline {
  --color-background: var(--bg-primary);
  --color-foreground: var(--text-primary);
  --font-sans: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  --font-mono: ui-monospace, SFMono-Regular, 'SF Mono', Consolas, 'Liberation Mono', Menlo, monospace;
}

* {
  box-sizing: border-box;
}

html {
  scroll-behavior: smooth;
}

body {
  background: var(--bg-primary);
  color: var(--text-primary);
  font-family: 'Inter', system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-weight: 400;
  line-height: 1.6;
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  overflow-x: hidden;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 6px;
}

::-webkit-scrollbar-track {
  background: var(--bg-secondary);
}

::-webkit-scrollbar-thumb {
  background: var(--border-secondary);
  border-radius: 3px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--primary-500);
}

/* Selection */
::selection {
  background: var(--primary-500);
  color: white;
}

/* Focus styles */
.focus-ring {
  @apply focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-offset-transparent;
}

/* Smooth animations */
@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* Utility classes */
.animate-fade-in-up {
  animation: fadeInUp 0.6s ease-out;
}

.animate-scale-in {
  animation: scaleIn 0.4s ease-out;
}

.animate-slide-in-right {
  animation: slideInRight 0.5s ease-out;
}

/* Glass morphism effect */
.glass {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

.dark .glass {
  background: rgba(0, 0, 0, 0.2);
  border: 1px solid rgba(255, 255, 255, 0.1);
}

/* Text gradient utilities */
.text-gradient {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Button hover effects */
.btn-primary {
  background: var(--gradient-primary);
  transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(139, 92, 246, 0.4);
}

.btn-primary:active {
  transform: translateY(0);
}

/* Professional Typography Classes (myZScore.ai Brand Kit) */
.text-display-large {
  font-family: 'Space Grotesk', 'Inter', sans-serif;
  font-size: var(--text-6xl);
  font-weight: 800;
  line-height: var(--leading-tight);
}

.text-display-medium {
  font-family: 'Space Grotesk', 'Inter', sans-serif;
  font-size: var(--text-5xl);
  font-weight: 700;
  line-height: var(--leading-snug);
}

.text-heading-1 {
  font-family: 'Space Grotesk', 'Inter', sans-serif;
  font-size: var(--text-4xl);
  font-weight: 700;
  line-height: var(--leading-normal);
}

.text-heading-2 {
  font-family: 'Space Grotesk', 'Inter', sans-serif;
  font-size: var(--text-3xl);
  font-weight: 600;
  line-height: var(--leading-normal);
}

.text-heading-3 {
  font-family: 'Space Grotesk', 'Inter', sans-serif;
  font-size: var(--text-2xl);
  font-weight: 600;
  line-height: var(--leading-relaxed);
}

.text-body-large {
  font-family: 'Inter', sans-serif;
  font-size: var(--text-lg);
  font-weight: 400;
  line-height: var(--leading-body);
}

.text-body-regular {
  font-family: 'Inter', sans-serif;
  font-size: var(--text-base);
  font-weight: 400;
  line-height: var(--leading-body);
}

.text-body-small {
  font-family: 'Inter', sans-serif;
  font-size: var(--text-sm);
  font-weight: 400;
  line-height: var(--leading-loose);
}

.text-button {
  font-family: 'Space Grotesk', 'Inter', sans-serif;
  font-weight: 600;
  text-transform: uppercase;
  letter-spacing: 0.025em;
}

/* Professional Spacing Utilities */
.space-professional > * + * {
  margin-top: var(--spacing-xl);
}

.space-compact > * + * {
  margin-top: var(--spacing-lg);
}

.space-tight > * + * {
  margin-top: var(--spacing-md);
}

/* Professional Card Styles */
.card-professional {
  background: var(--surface);
  border: 1px solid var(--border-primary);
  border-radius: 1rem;
  padding: var(--spacing-2xl);
  box-shadow: 0 1px 3px 0 rgba(0, 0, 0, 0.1), 0 1px 2px 0 rgba(0, 0, 0, 0.06);
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.card-professional:hover {
  box-shadow: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
  border-color: var(--primary-500);
  transform: translateY(-1px);
}

/* Professional Input Styles */
.input-professional {
  background: transparent;
  border: none;
  outline: none;
  font-family: 'Inter', sans-serif;
  font-size: var(--text-base);
  color: var(--text-primary);
  line-height: var(--leading-body);
}

.input-professional::placeholder {
  color: var(--text-tertiary);
}

/* Line Clamp Utilities */
.line-clamp-1 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
}

.line-clamp-2 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
}

.line-clamp-3 {
  overflow: hidden;
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
}

/* Enhanced Accessibility */
@media (prefers-reduced-motion: reduce) {
  *,
  *::before,
  *::after {
    animation-duration: 0.01ms !important;
    animation-iteration-count: 1 !important;
    transition-duration: 0.01ms !important;
    scroll-behavior: auto !important;
  }
}

/* Focus Visible for Better Accessibility */
.focus-visible:focus-visible {
  outline: 2px solid var(--primary-500);
  outline-offset: 2px;
}

/* Enhanced Contrast for Dark Mode - Already defined above */

/* Dynamic Theme Classes - Auto-adapting to theme changes */
.theme-text-primary {
  color: var(--text-primary);
}

.theme-text-secondary {
  color: var(--text-secondary);
}

.theme-text-tertiary {
  color: var(--text-tertiary);
}

.theme-bg-primary {
  background-color: var(--bg-primary);
}

.theme-bg-secondary {
  background-color: var(--bg-secondary);
}

.theme-surface {
  background-color: var(--surface);
}

.theme-border-primary {
  border-color: var(--border-primary);
}

.theme-primary {
  color: var(--primary-500);
}

.theme-bg-gradient-primary {
  background: var(--gradient-primary);
}

/* Enhanced gradient text that works in both themes */
.theme-gradient-text {
  background: var(--gradient-primary);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
  /* Fallback for browsers that don't support background-clip: text */
  color: var(--primary-500);
}

/* Ensure proper contrast in all themes */
@supports not (-webkit-background-clip: text) {
  .theme-gradient-text {
    color: var(--primary-500);
  }
}

/* Professional Hover States */
.hover-lift:hover {
  transform: translateY(-1px);
}

.hover-scale:hover {
  transform: scale(1.02);
}

/* Professional Shadows */
.shadow-brand {
  box-shadow: 0 4px 14px 0 rgba(139, 92, 246, 0.15);
}

.shadow-brand-lg {
  box-shadow: 0 10px 25px 0 rgba(139, 92, 246, 0.25);
}

/* Gradient Text Utilities */
.text-gradient-multi {
  background: var(--gradient-multi);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Professional Border Radius */
.rounded-brand {
  border-radius: 1rem;
}

.rounded-brand-lg {
  border-radius: 1.5rem;
}

/* Professional Micro-interactions */
@keyframes gentlePulse {
  0%, 100% {
    opacity: 1;
    transform: scale(1);
  }
  50% {
    opacity: 0.8;
    transform: scale(1.02);
  }
}

@keyframes subtleFloat {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-2px);
  }
}

@keyframes smoothSlideIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes smoothFadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes buttonPress {
  0% { transform: scale(1); }
  50% { transform: scale(0.98); }
  100% { transform: scale(1); }
}

@keyframes cloudDrift {
  0% { transform: translateX(-10px) translateY(-5px); }
  50% { transform: translateX(10px) translateY(5px); }
  100% { transform: translateX(-10px) translateY(-5px); }
}

@keyframes pulseSoft {
  0%, 100% { opacity: 0.6; }
  50% { opacity: 1; }
}

@keyframes bounce {
  0%, 20%, 53%, 80%, 100% {
    transform: translate3d(0, 0, 0);
  }
  40%, 43% {
    transform: translate3d(0, -8px, 0);
  }
  70% {
    transform: translate3d(0, -4px, 0);
  }
  90% {
    transform: translate3d(0, -2px, 0);
  }
}

/* Professional Animation Classes */
.animate-gentle-pulse {
  animation: gentlePulse 3s ease-in-out infinite;
}

.animate-subtle-float {
  animation: subtleFloat 4s ease-in-out infinite;
}

.animate-smooth-slide-in {
  animation: smoothSlideIn 0.4s ease-out;
}

.animate-smooth-fade-in {
  animation: smoothFadeIn 0.3s ease-out;
}

.animate-button-press {
  animation: buttonPress 0.15s ease-out;
}

.animate-cloud-drift {
  animation: cloudDrift 20s ease-in-out infinite;
}

.animate-pulse-soft {
  animation: pulseSoft 2s ease-in-out infinite;
}

.animate-bounce {
  animation: bounce 1s infinite;
}

/* Cloud Background Classes - Maximum Specificity */
.chat-container {
  position: relative;
  height: 100vh;
  overflow: hidden;
}

.chat-container .cloud-background {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-color: #EDE9FE; /* Fallback color */
  background: linear-gradient(135deg, #F8FAFC 0%, #EDE9FE 25%, #DDD6FE 50%, #C4B5FD 75%, #A78BFA 100%) !important;
  z-index: 1;
}

.chat-container .cloud-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background:
    radial-gradient(ellipse 800px 600px at 20% 10%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse 600px 400px at 80% 30%, rgba(167, 139, 250, 0.12) 0%, transparent 50%),
    radial-gradient(ellipse 400px 300px at 40% 70%, rgba(196, 181, 253, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse 500px 350px at 70% 80%, rgba(221, 214, 254, 0.08) 0%, transparent 50%) !important;
  z-index: 2;
}

/* Dark theme cloud backgrounds */
html.dark .chat-container .cloud-background {
  background-color: #1E293B; /* Fallback color */
  background: linear-gradient(135deg, #0F172A 0%, #1E293B 25%, #312E81 50%, #4C1D95 75%, #5B21B6 100%) !important;
}

html.dark .chat-container .cloud-overlay {
  background:
    radial-gradient(ellipse 800px 600px at 20% 10%, rgba(167, 139, 250, 0.2) 0%, transparent 50%),
    radial-gradient(ellipse 600px 400px at 80% 30%, rgba(196, 181, 253, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse 400px 300px at 40% 70%, rgba(221, 214, 254, 0.12) 0%, transparent 50%),
    radial-gradient(ellipse 500px 350px at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%) !important;
}

/* Ensure main content is above background */
.chat-container .chat-content {
  position: relative;
  z-index: 10;
  height: 100%;
}

/* Enhanced Interactive States */
.interactive-card {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  cursor: pointer;
}

.interactive-card:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.1);
}

.interactive-card:active {
  transform: translateY(0);
  transition-duration: 0.1s;
}

.interactive-button {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
  position: relative;
  overflow: hidden;
}

.interactive-button::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.interactive-button:hover::before {
  left: 100%;
}

.interactive-button:hover {
  transform: translateY(-1px);
  box-shadow: 0 4px 12px rgba(139, 92, 246, 0.3);
}

.interactive-button:active {
  transform: translateY(0);
}

/* Smooth Focus States */
.focus-smooth {
  transition: all 0.2s cubic-bezier(0.4, 0, 0.2, 1);
}

.focus-smooth:focus {
  outline: none;
  box-shadow: 0 0 0 3px rgba(139, 92, 246, 0.3);
  border-color: var(--primary-500);
}

/* Professional Loading States */
.loading-shimmer {
  background: linear-gradient(90deg,
    var(--surface) 25%,
    var(--bg-secondary) 50%,
    var(--surface) 75%
  );
  background-size: 200% 100%;
  animation: shimmer 1.5s infinite;
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

/* Mobile-specific optimizations */
@media (max-width: 640px) {
  .card-professional {
    padding: 1rem;
  }

  .text-heading-1 {
    font-size: 1.875rem;
    line-height: 2.25rem;
  }

  .text-heading-2 {
    font-size: 1.5rem;
    line-height: 2rem;
  }

  .text-heading-3 {
    font-size: 1.25rem;
    line-height: 1.75rem;
  }

  /* Adjust cloud background for mobile */
  .cloud-overlay {
    background:
      radial-gradient(ellipse 600px 400px at 20% 10%, rgba(139, 92, 246, 0.12) 0%, transparent 50%),
      radial-gradient(ellipse 400px 300px at 80% 30%, rgba(167, 139, 250, 0.1) 0%, transparent 50%),
      radial-gradient(ellipse 300px 200px at 40% 70%, rgba(196, 181, 253, 0.08) 0%, transparent 50%);
  }

  .dark .cloud-overlay {
    background:
      radial-gradient(ellipse 600px 400px at 20% 10%, rgba(167, 139, 250, 0.15) 0%, transparent 50%),
      radial-gradient(ellipse 400px 300px at 80% 30%, rgba(196, 181, 253, 0.12) 0%, transparent 50%),
      radial-gradient(ellipse 300px 200px at 40% 70%, rgba(221, 214, 254, 0.1) 0%, transparent 50%);
  }
}
