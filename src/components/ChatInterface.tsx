'use client';

import { useState, useRef, useEffect } from 'react';
import { Send, Plus, Sparkles, Sun, Moon, Bot, User, TrendingUp, BarChart3, Wallet, Shield, ChevronDown, ChevronUp } from 'lucide-react';
import { motion, AnimatePresence } from 'framer-motion';
import clsx from 'clsx';
import { componentClasses, cn, toggleTheme } from '@/utils/theme';
import { sendQuery, ApiServiceError } from '@/services/api';
// import { useAuth } from '@/contexts/AuthContext';
// import { chatService, type Chat as ChatType } from '@/services/chatService';
// import Sidebar from './Sidebar';

interface Message {
  id: string;
  content: string;
  is_user: boolean;
  created_at: string;
}

interface Chat {
  id: string;
  title: string;
  messages: Message[];
  created_at: string;
  updated_at: string;
}

const SUGGESTED_QUESTIONS = [
  {
    question: "What's my zScore?",
    icon: TrendingUp,
    description: "Check your current reputation score"
  },
  {
    question: "How do I improve my score?",
    icon: BarChart3,
    description: "Get personalized recommendations"
  },
  {
    question: "Show my lending history",
    icon: Wallet,
    description: "View your transaction history"
  },
  {
    question: "What are the security features?",
    icon: Shield,
    description: "Learn about our security measures"
  }
];

function getMockResponse(prompt: string): string {
  const lowerPrompt = prompt.toLowerCase();

  if (lowerPrompt.includes('zscore') || lowerPrompt.includes('score')) {
    return `## Your zScore Analysis

**Current zScore: 742** 🎯

Your reputation score is in the **Good** range, indicating solid trustworthiness in the DeFi ecosystem.

**Score Breakdown:**
- **Payment History**: 85% (Excellent)
- **Credit Utilization**: 72% (Good)
- **Account Age**: 68% (Fair)
- **Transaction Diversity**: 91% (Excellent)
- **Network Activity**: 79% (Good)

**Recent Activity:**
- Last 30 days: +12 points
- Successful transactions: 47/50
- Average transaction value: $2,340
- Network participation: High

**Peer Comparison:**
- Top 25% of users in your category
- Above average for 6-month users
- Strong DeFi engagement score

*Your zScore is updated in real-time based on on-chain activity.*`;
  }

  if (lowerPrompt.includes('improve') || lowerPrompt.includes('recommendations')) {
    return `## How to Improve Your zScore

**Immediate Actions (1-2 weeks):**
1. **Increase Transaction Frequency**: Aim for 3-5 transactions per week
2. **Diversify Protocols**: Interact with 2-3 new DeFi protocols
3. **Maintain Consistency**: Keep regular on-chain activity

**Medium-term Goals (1-3 months):**
1. **Build Credit History**: Complete more lending/borrowing cycles
2. **Network Participation**: Join governance voting in DAOs
3. **Risk Management**: Maintain healthy collateralization ratios

**Long-term Strategy (3+ months):**
1. **Ecosystem Leadership**: Become active in community governance
2. **Protocol Loyalty**: Build long-term relationships with key protocols
3. **Advanced DeFi**: Explore yield farming and liquidity provision

**Quick Wins:**
- ✅ Complete your profile verification (+15 points)
- ✅ Link additional wallets (+10 points)
- ✅ Participate in community discussions (+5 points)

**Potential Score Impact:**
Following these recommendations could increase your score by 50-80 points over 3 months.

*Personalized recommendations based on your current activity patterns.*`;
  }

  if (lowerPrompt.includes('lending') || lowerPrompt.includes('history')) {
    return `## Your Lending History

**Overview:**
- Total loans: 23 completed
- Total borrowed: $127,450
- Total repaid: $132,890
- Average APR: 4.2%
- Perfect repayment record: 100%

**Recent Transactions:**
1. **USDC Loan** - $5,000 @ 3.8% APR (Repaid)
   - Platform: Aave V3
   - Duration: 45 days
   - Status: ✅ Completed

2. **ETH Collateral** - $12,000 borrowed against 8 ETH
   - Platform: Compound
   - Duration: 30 days
   - Status: ✅ Completed

3. **DAI Loan** - $3,200 @ 4.1% APR
   - Platform: MakerDAO
   - Duration: 60 days
   - Status: ✅ Completed

**Performance Metrics:**
- On-time repayment rate: 100%
- Average loan duration: 42 days
- Preferred collateral: ETH (65%), USDC (25%), Other (10%)
- Risk level: Conservative

**Credit Utilization:**
- Current utilization: 23%
- Recommended range: 10-30%
- Available credit: $45,000

*All data verified on-chain and updated in real-time.*`;
  }

  if (lowerPrompt.includes('security') || lowerPrompt.includes('features')) {
    return `## Security Features & Protection

**Multi-Layer Security:**

**🔐 Smart Contract Security:**
- Audited by leading security firms
- Formal verification of critical functions
- Bug bounty program with $500K+ rewards
- Real-time monitoring and alerts

**🛡️ User Protection:**
- Non-custodial architecture (you control your keys)
- Zero-knowledge proofs for privacy
- Encrypted data transmission
- Multi-signature wallet support

**🔍 Risk Assessment:**
- Real-time protocol risk scoring
- Automated liquidation protection
- Slippage protection on trades
- MEV protection mechanisms

**📊 Transparency:**
- Open-source smart contracts
- Public audit reports
- Real-time TVL and metrics
- Community governance

**🚨 Emergency Features:**
- Circuit breakers for extreme volatility
- Emergency pause functionality
- Insurance fund protection
- 24/7 monitoring systems

**Privacy Protection:**
- Your personal data is never stored
- Only on-chain activity is analyzed
- GDPR compliant data handling
- Optional privacy mode available

**Best Practices:**
- Always verify contract addresses
- Use hardware wallets for large amounts
- Enable transaction notifications
- Regular security checkups

*Security is our top priority. Report any <NAME_EMAIL>*`;
  }

  return `## Welcome to zScore

Thank you for your question: "${prompt}"

I'm your AI assistant for navigating the world of decentralized reputation and credit scoring.

**What I can help with:**
- 📊 zScore analysis and insights
- 💡 Personalized improvement recommendations
- 📈 Lending and borrowing guidance
- 🔒 Security and best practices
- 🌐 DeFi ecosystem navigation

**Getting Started:**
Your zScore is calculated based on your on-chain activity, including:
- Transaction history and patterns
- DeFi protocol interactions
- Lending/borrowing behavior
- Network participation
- Risk management practices

**Popular Questions:**
- "What's my current zScore?"
- "How can I improve my score?"
- "Show me my lending history"
- "What security features protect me?"

*Connect your wallet to get personalized insights and start building your decentralized reputation.*`;
}

export default function ChatInterface() {
  // Demo mode - authentication disabled
  const [currentChat, setCurrentChat] = useState<Chat | null>(null);
  const [input, setInput] = useState('');
  const [isLoading, setIsLoading] = useState(false);
  const [theme, setTheme] = useState<'light' | 'dark'>('light');
  const [showWelcome, setShowWelcome] = useState(true);
  const [showQuestionDropdown, setShowQuestionDropdown] = useState(false);
  const messagesEndRef = useRef<HTMLDivElement>(null);
  const dropdownRef = useRef<HTMLDivElement>(null);

  // Initialize theme on component mount
  useEffect(() => {
    const isDark = document.documentElement.classList.contains('dark');
    setTheme(isDark ? 'dark' : 'light');
  }, []);

  // Handle click outside dropdown
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (dropdownRef.current && !dropdownRef.current.contains(event.target as Node)) {
        setShowQuestionDropdown(false);
      }
    };

    if (showQuestionDropdown) {
      document.addEventListener('mousedown', handleClickOutside);
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside);
    };
  }, [showQuestionDropdown]);

  const scrollToBottom = () => {
    messagesEndRef.current?.scrollIntoView({ behavior: 'smooth' });
  };

  useEffect(() => {
    scrollToBottom();
  }, [currentChat?.messages]);

  const createNewChat = async () => {
    // Demo mode - create local chat only
    const newChat: Chat = {
      id: Date.now().toString(),
      title: 'New Chat',
      messages: [],
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };
    setCurrentChat(newChat);
    setShowWelcome(true); // Reset to welcome screen for new chat
  };

  const sendMessage = async (messageContent: string) => {
    if (!messageContent.trim()) return;

    // Hide welcome screen on first message
    if (showWelcome) {
      setShowWelcome(false);
    }

    // Create new chat if none exists
    if (!currentChat) {
      // Demo mode - create local chat only
      const newChat: Chat = {
        id: Date.now().toString(),
        title: messageContent.slice(0, 50) + (messageContent.length > 50 ? '...' : ''),
        messages: [],
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      };
      setCurrentChat(newChat);
    }

    // Add user message
    const userMessage: Message = {
      id: Date.now().toString(),
      content: messageContent,
      is_user: true,
      created_at: new Date().toISOString()
    };

    setCurrentChat(prev => prev ? {
      ...prev,
      messages: [...(prev.messages || []), userMessage]
    } : null);

    setInput('');
    setIsLoading(true);

    try {
      console.log('Sending message to API:', messageContent);

      // Call the API using the service
      const data = await sendQuery(messageContent);

      console.log('Data received from API:', data);

      // Add AI response
      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: data.result || 'No response received',
        is_user: false,
        created_at: new Date().toISOString()
      };

      setCurrentChat(prev => prev ? {
        ...prev,
        messages: [...(prev.messages || []), aiMessage]
      } : null);

    } catch (error) {
      // Handle API service errors and fall back to mock response for demonstration
      if (error instanceof ApiServiceError) {
        console.log('API service error, using mock response for demonstration:', error.message);
      } else {
        console.log('Unexpected error, using mock response for demonstration:', error);
      }

      // Provide a mock response for demonstration
      const mockResponse = getMockResponse(messageContent);

      const aiMessage: Message = {
        id: (Date.now() + 1).toString(),
        content: mockResponse,
        is_user: false,
        created_at: new Date().toISOString()
      };

      setCurrentChat(prev => prev ? {
        ...prev,
        messages: [...(prev.messages || []), aiMessage]
      } : null);
    } finally {
      setIsLoading(false);
    }
  };

  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault();
    sendMessage(input);
  };

  const handleKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter' && !e.shiftKey) {
      e.preventDefault();
      if (input.trim() && !isLoading) {
        sendMessage(input);
      }
    }
  };

  const handleSuggestedQuestion = (question: string) => {
    setInput(question);
    sendMessage(question);
  };

  // Demo mode - sidebar functions disabled

  const lightCloudBg = 'linear-gradient(135deg, #F8FAFC 0%, #EDE9FE 25%, #DDD6FE 50%, #C4B5FD 75%, #A78BFA 100%)';
  const darkCloudBg = 'linear-gradient(135deg, #0F172A 0%, #1E293B 25%, #312E81 50%, #4C1D95 75%, #5B21B6 100%)';

  const lightCloudOverlay = `
    radial-gradient(ellipse 800px 600px at 20% 10%, rgba(139, 92, 246, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse 600px 400px at 80% 30%, rgba(167, 139, 250, 0.12) 0%, transparent 50%),
    radial-gradient(ellipse 400px 300px at 40% 70%, rgba(196, 181, 253, 0.1) 0%, transparent 50%),
    radial-gradient(ellipse 500px 350px at 70% 80%, rgba(221, 214, 254, 0.08) 0%, transparent 50%)
  `;

  const darkCloudOverlay = `
    radial-gradient(ellipse 800px 600px at 20% 10%, rgba(167, 139, 250, 0.2) 0%, transparent 50%),
    radial-gradient(ellipse 600px 400px at 80% 30%, rgba(196, 181, 253, 0.15) 0%, transparent 50%),
    radial-gradient(ellipse 400px 300px at 40% 70%, rgba(221, 214, 254, 0.12) 0%, transparent 50%),
    radial-gradient(ellipse 500px 350px at 70% 80%, rgba(139, 92, 246, 0.1) 0%, transparent 50%)
  `;

  return (
    <div className="chat-container">
      {/* Sidebar disabled in demo mode */}
      {/* Cloud-like Background */}
      <div
        className="cloud-background"
        style={{
          background: theme === 'dark' ? darkCloudBg : lightCloudBg
        }}
      ></div>
      <div
        className="cloud-overlay animate-cloud-drift"
        style={{
          background: theme === 'dark' ? darkCloudOverlay : lightCloudOverlay
        }}
      ></div>

      {/* Main Chat Area */}
      <div className="chat-content h-full flex flex-col">
        {/* Header - Only show when not on welcome screen */}
        <AnimatePresence>
          {!showWelcome && (
            <motion.div
              initial={{ y: -60, opacity: 0 }}
              animate={{ y: 0, opacity: 1 }}
              exit={{ y: -60, opacity: 0 }}
              transition={{ duration: 0.3, ease: "easeInOut" }}
              className="relative border-b border-border-primary bg-surface/80 backdrop-blur-xl"
            >
              <div className="absolute inset-0 bg-gradient-mesh opacity-5"></div>
              <div className="relative p-4 sm:p-6">
                <div className="flex items-center justify-between">
                  <div className="flex items-center gap-2 sm:gap-4">
                    <button
                      onClick={createNewChat}
                      className="interactive-button flex items-center gap-2 px-3 sm:px-4 py-2 bg-gradient-primary text-white rounded-xl text-button group"
                    >
                      <Plus size={16} className="group-hover:rotate-90 transition-transform duration-300" />
                      <span className="text-sm hidden sm:inline">NEW CHAT</span>
                      <span className="text-sm sm:hidden">NEW</span>
                    </button>
                    <div className="flex items-center gap-2 sm:gap-3">
                      <div className="relative">
                        <Sparkles className="animate-gentle-pulse" style={{ color: 'var(--primary-500)' }} size={20} />
                        <div className="absolute inset-0 bg-primary-500 rounded-full blur-lg opacity-20 animate-gentle-pulse"></div>
                      </div>
                      <div className="flex flex-col justify-center">
                        <h1 className="text-lg sm:text-xl font-space-grotesk font-semibold leading-none theme-gradient-text">
                          zScore
                        </h1>
                        <p className="text-xs sm:text-sm leading-none mt-1 hidden sm:block" style={{ color: 'var(--text-secondary)' }}>
                          AI-Powered DeFi Assistant
                        </p>
                      </div>
                    </div>
                  </div>
                  <button
                    onClick={() => {
                      const newTheme = toggleTheme(theme);
                      setTheme(newTheme);
                    }}
                    className={cn(componentClasses.button.secondary, 'hover:bg-secondary')}
                    title="Toggle theme"
                  >
                    {theme === 'dark' ?
                      <Sun
                        size={20}
                        className="group-hover:rotate-12 transition-all duration-200"
                        style={{
                          color: 'var(--text-secondary)',
                          transition: 'color 0.2s ease'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
                        onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                      /> :
                      <Moon
                        size={20}
                        className="group-hover:-rotate-12 transition-all duration-200"
                        style={{
                          color: 'var(--text-secondary)',
                          transition: 'color 0.2s ease'
                        }}
                        onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
                        onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                      />
                    }
                  </button>
                </div>
              </div>
            </motion.div>
          )}
        </AnimatePresence>

        {/* Messages Area */}
        <div className="flex-1 overflow-y-auto relative">
          <div className="absolute inset-0 bg-gradient-mesh opacity-[0.02]"></div>

          <AnimatePresence mode="wait">
            {showWelcome ? (
              /* Welcome Screen */
              <motion.div
                key="welcome"
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.5, ease: "easeInOut" }}
                className="h-full flex flex-col items-center justify-center max-w-5xl mx-auto px-4 sm:px-6 relative pb-32"
                style={{ minHeight: 'calc(100vh - 200px)' }}
              >
                {/* Theme Toggle - Top Right */}
                <motion.button
                  initial={{ opacity: 0, scale: 0.8 }}
                  animate={{ opacity: 1, scale: 1 }}
                  transition={{ duration: 0.3, delay: 0.8 }}
                  onClick={() => {
                    const newTheme = toggleTheme(theme);
                    setTheme(newTheme);
                  }}
                  className={cn('absolute top-4 right-4 sm:top-8 sm:right-8', componentClasses.button.secondary, 'hover:bg-secondary')}
                  title="Toggle theme"
                >
                  {theme === 'dark' ?
                    <Sun
                      size={20}
                      className="group-hover:rotate-12 transition-all duration-200"
                      style={{
                        color: 'var(--text-secondary)',
                        transition: 'color 0.2s ease'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
                      onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                    /> :
                    <Moon
                      size={20}
                      className="group-hover:-rotate-12 transition-all duration-200"
                      style={{
                        color: 'var(--text-secondary)',
                        transition: 'color 0.2s ease'
                      }}
                      onMouseEnter={(e) => e.currentTarget.style.color = 'var(--text-primary)'}
                      onMouseLeave={(e) => e.currentTarget.style.color = 'var(--text-secondary)'}
                    />
                  }
                </motion.button>
                {/* Welcome Section - Compact Professional Design */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.1 }}
                  className="text-center mb-12"
                >
                  <div className="relative mb-6">
                    <motion.div
                      initial={{ scale: 0.9, opacity: 0 }}
                      animate={{ scale: 1, opacity: 1 }}
                      transition={{ duration: 0.4, delay: 0.2 }}
                      className="w-16 h-16 mx-auto bg-gradient-primary rounded-2xl flex items-center justify-center shadow-medium animate-subtle-float"
                    >
                      <Sparkles className="text-white" size={28} />
                    </motion.div>
                  </div>
                  <motion.h1
                    initial={{ opacity: 0, y: 15 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.3 }}
                    className={componentClasses.welcome.title}
                  >
                    Welcome to{' '}
                    <span className="theme-gradient-text font-space-grotesk font-semibold">
                      zScore agent
                    </span>
                  </motion.h1>
                  <motion.p
                    initial={{ opacity: 0, y: 15 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.4 }}
                    className="text-sm sm:text-body-large max-w-lg mx-auto mb-3 text-center leading-relaxed px-4" style={{ color: 'var(--text-secondary)' }}
                  >
                    Your AI-powered assistant for decentralized reputation, credit scoring, and DeFi insights.
                  </motion.p>
                  <motion.div
                    initial={{ opacity: 0, y: 15 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.5, delay: 0.5 }}
                    className="flex items-center justify-center gap-2 text-body-small" style={{ color: 'var(--text-tertiary)' }}
                  >
                    <div
                      className="w-1.5 h-1.5 rounded-full animate-pulse-soft"
                      style={{ backgroundColor: theme === 'dark' ? '#FBBF24' : '#F59E0B' }}
                    ></div>
                    <span>Demo Mode</span>
                  </motion.div>
                </motion.div>

                {/* What to ask section - Professional Layout */}
                <motion.div
                  initial={{ opacity: 0, y: 20 }}
                  animate={{ opacity: 1, y: 0 }}
                  transition={{ duration: 0.5, delay: 0.6 }}
                  className="w-full max-w-3xl mb-8"
                >
                  <h2 className="text-lg sm:text-heading-3 mb-4 sm:mb-6 text-center font-space-grotesk" style={{ color: 'var(--text-primary)' }}>
                    What to ask
                  </h2>
                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4 mb-6">
                    {SUGGESTED_QUESTIONS.map((item, index) => {
                      const IconComponent = item.icon;
                      return (
                        <motion.button
                          key={index}
                          initial={{ opacity: 0, y: 15 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ duration: 0.3, delay: 0.7 + index * 0.05 }}
                          onClick={() => handleSuggestedQuestion(item.question)}
                          className="card-professional interactive-card group text-left hover:border-primary-500/30"
                        >
                          <div className="flex items-start gap-3">
                            <div className="w-10 h-10 bg-gradient-primary rounded-xl flex items-center justify-center flex-shrink-0 group-hover:scale-105 transition-transform duration-200">
                              <IconComponent className="text-white" size={20} />
                            </div>
                            <div className="flex-1 min-w-0">
                              <h3 className="text-body-regular font-medium transition-colors duration-200 mb-1 line-clamp-2" style={{ color: 'var(--text-primary)' }}>
                                {item.question}
                              </h3>
                              <p className="text-body-small transition-colors duration-200 line-clamp-2" style={{ color: 'var(--text-secondary)' }}>
                                {item.description}
                              </p>
                            </div>
                          </div>
                        </motion.button>
                      );
                    })}
                  </div>
                </motion.div>
              </motion.div>
            ) : (
              /* Chat Messages */
              <motion.div
                key="chat"
                initial={{ opacity: 0, x: 20 }}
                animate={{ opacity: 1, x: 0 }}
                exit={{ opacity: 0, x: -20 }}
                transition={{ duration: 0.4, ease: "easeInOut" }}
                className="relative p-4 sm:p-6"
              >
                {!currentChat || !currentChat.messages || currentChat.messages.length === 0 ? (
                  <div className="h-full flex flex-col items-center justify-center max-w-2xl mx-auto min-h-[50vh]">
                    <div className="text-center">
                      <div className="w-12 h-12 mx-auto bg-gradient-primary rounded-xl flex items-center justify-center shadow-medium mb-4">
                        <Sparkles className="text-white" size={24} />
                      </div>
                      <h2 className="text-heading-3 mb-3 font-space-grotesk" style={{ color: 'var(--text-primary)' }}>
                        Start a conversation
                      </h2>
                      <p className="text-body-regular mb-6" style={{ color: 'var(--text-secondary)' }}>
                        Ask me anything about your zScore, lending history, or DeFi insights.
                      </p>

                      {/* Quick action buttons */}
                      <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 max-w-lg mx-auto">
                        {SUGGESTED_QUESTIONS.slice(0, 2).map((item, index) => {
                          const IconComponent = item.icon;
                          return (
                            <button
                              key={index}
                              onClick={() => handleSuggestedQuestion(item.question)}
                              className="card-professional interactive-card group text-left hover:border-primary-500/30 p-4"
                            >
                              <div className="flex items-center gap-3">
                                <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center flex-shrink-0 group-hover:scale-105 transition-transform duration-200">
                                  <IconComponent className="text-white" size={16} />
                                </div>
                                <span className="text-body-small font-medium transition-colors duration-200 line-clamp-2" style={{ color: 'var(--text-primary)' }}>
                                  {item.question}
                                </span>
                              </div>
                            </button>
                          );
                        })}
                      </div>
                    </div>
                  </div>
                ) : (
                  <div className="max-w-3xl mx-auto space-y-6">
                    {(currentChat.messages || []).map((message, index) => (
                      <motion.div
                        key={message.id}
                        initial={{ opacity: 0, y: 15 }}
                        animate={{ opacity: 1, y: 0 }}
                        transition={{ duration: 0.3, delay: index * 0.05 }}
                        className={clsx(
                          "flex gap-3",
                          message.is_user ? "justify-end" : "justify-start"
                        )}
                      >
                        {!message.is_user && (
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center shadow-soft">
                              <Bot className="text-white" size={16} />
                            </div>
                          </div>
                        )}
                        <div
                          className={clsx(
                            "max-w-[85%] sm:max-w-[80%] group",
                            message.is_user ? "flex flex-col items-end" : ""
                          )}
                        >
                          <div
                            className={clsx(
                              "px-4 py-3 rounded-2xl transition-all duration-200",
                              message.is_user
                                ? "bg-gradient-primary text-white shadow-soft"
                                : "bg-surface border border-border-primary shadow-soft hover:shadow-medium"
                            )}
                          >
                            <div
                              className="text-body-regular whitespace-pre-wrap"
                              style={{
                                color: message.is_user ? 'white' : 'var(--text-primary)'
                              }}
                            >
                              {message.content}
                            </div>
                          </div>
                          <div
                            className="text-body-small mt-1 px-2 opacity-60"
                            style={{
                              color: message.is_user ? 'var(--text-secondary)' : 'var(--text-tertiary)'
                            }}
                          >
                            {new Date(message.created_at).toLocaleTimeString([], { hour: '2-digit', minute: '2-digit' })}
                          </div>
                        </div>
                        {message.is_user && (
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-accent-cyan rounded-lg flex items-center justify-center shadow-soft">
                              <User className="text-white" size={16} />
                            </div>
                          </div>
                        )}
                      </motion.div>
                    ))}
                    {isLoading && (
                      <motion.div
                        initial={{ opacity: 0, y: 15 }}
                        animate={{ opacity: 1, y: 0 }}
                        className="flex justify-start"
                      >
                        <div className="flex gap-3">
                          <div className="flex-shrink-0">
                            <div className="w-8 h-8 bg-gradient-primary rounded-lg flex items-center justify-center shadow-soft">
                              <Bot className="text-white" size={16} />
                            </div>
                          </div>
                          <div className="bg-surface border border-border-primary px-4 py-3 rounded-2xl shadow-soft">
                            <div className="flex items-center gap-3">
                              <div className="flex gap-1">
                                <div
                                  className="w-2 h-2 rounded-full"
                                  style={{
                                    backgroundColor: theme === 'dark' ? '#A78BFA' : '#8B5CF6',
                                    animation: 'bounce 1s infinite'
                                  }}
                                ></div>
                                <div
                                  className="w-2 h-2 rounded-full"
                                  style={{
                                    backgroundColor: theme === 'dark' ? '#A78BFA' : '#8B5CF6',
                                    animation: 'bounce 1s infinite',
                                    animationDelay: '0.1s'
                                  }}
                                ></div>
                                <div
                                  className="w-2 h-2 rounded-full"
                                  style={{
                                    backgroundColor: theme === 'dark' ? '#A78BFA' : '#8B5CF6',
                                    animation: 'bounce 1s infinite',
                                    animationDelay: '0.2s'
                                  }}
                                ></div>
                              </div>
                              <span className="text-body-small" style={{ color: 'var(--text-secondary)' }}>
                                AI is thinking...
                              </span>
                            </div>
                          </div>
                        </div>
                      </motion.div>
                    )}
                    <div ref={messagesEndRef} />
                  </div>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        {/* Input Area */}
        <motion.div
          initial={showWelcome ? { y: 100, opacity: 0 } : { y: 0, opacity: 1 }}
          animate={{ y: 0, opacity: 1 }}
          transition={{ duration: 0.5, ease: "easeInOut" }}
          className={clsx(
            "transition-all duration-300 z-10",
            showWelcome
              ? "fixed bottom-4 sm:bottom-8 left-1/2 transform -translate-x-1/2 w-full max-w-4xl px-4 sm:px-6"
              : "border-t border-border-primary bg-surface/80 backdrop-blur-xl"
          )}
        >
          <div className={clsx("p-4 sm:p-6", showWelcome && "p-3 sm:p-4")}>
            {/* Question Suggestions Dropdown */}
            {!showWelcome && (
              <div className="max-w-3xl mx-auto mb-3">
                <div className="relative" ref={dropdownRef}>
                  <button
                    type="button"
                    onClick={() => setShowQuestionDropdown(!showQuestionDropdown)}
                    className="flex items-center gap-2 px-3 py-2 text-sm rounded-lg border border-border-primary bg-surface hover:bg-surface-elevated transition-all duration-200 text-text-secondary hover:text-text-primary"
                  >
                    <Sparkles size={14} />
                    <span>Need inspiration?</span>
                    {showQuestionDropdown ? <ChevronUp size={14} /> : <ChevronDown size={14} />}
                  </button>

                  <AnimatePresence>
                    {showQuestionDropdown && (
                      <motion.div
                        initial={{ opacity: 0, y: -10, scale: 0.95 }}
                        animate={{ opacity: 1, y: 0, scale: 1 }}
                        exit={{ opacity: 0, y: -10, scale: 0.95 }}
                        transition={{ duration: 0.2, ease: "easeOut" }}
                        className="absolute top-full left-0 right-0 mt-2 bg-surface border border-border-primary rounded-xl shadow-large backdrop-blur-xl z-50 overflow-hidden"
                      >
                        <div className="p-2">
                          <div className="text-xs font-medium text-text-tertiary px-3 py-2 border-b border-border-primary mb-2">
                            Try asking about:
                          </div>
                          {SUGGESTED_QUESTIONS.map((item, index) => {
                            const IconComponent = item.icon;
                            return (
                              <button
                                key={index}
                                onClick={() => {
                                  handleSuggestedQuestion(item.question);
                                  setShowQuestionDropdown(false);
                                }}
                                className="w-full flex items-center gap-3 px-3 py-2 rounded-lg hover:bg-surface-elevated transition-colors duration-200 text-left group"
                              >
                                <div className="w-6 h-6 bg-gradient-primary rounded-md flex items-center justify-center flex-shrink-0 group-hover:scale-105 transition-transform duration-200">
                                  <IconComponent className="text-white" size={12} />
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="text-sm font-medium text-text-primary line-clamp-1">
                                    {item.question}
                                  </div>
                                  <div className="text-xs text-text-secondary line-clamp-1">
                                    {item.description}
                                  </div>
                                </div>
                              </button>
                            );
                          })}
                        </div>
                      </motion.div>
                    )}
                  </AnimatePresence>
                </div>
              </div>
            )}

            <form onSubmit={handleSubmit} className={clsx(showWelcome ? "w-full" : "max-w-3xl mx-auto")}>
              <div className="relative">
                <div className={clsx(
                  "flex gap-2 sm:gap-3 p-3 sm:p-4 rounded-2xl border shadow-soft focus-within:shadow-medium focus-within:border-primary-500/50 transition-all duration-300",
                  showWelcome
                    ? "bg-surface/95 backdrop-blur-xl border-border-primary shadow-large"
                    : "bg-surface border-border-primary"
                )}>
                  <input
                    type="text"
                    value={input}
                    onChange={(e) => setInput(e.target.value)}
                    onKeyDown={handleKeyDown}
                    placeholder={showWelcome ? "Ask about your zScore, lending history, or DeFi insights..." : "Ask about your zScore or DeFi insights..."}
                    className="input-professional flex-1 py-1 text-sm sm:text-base"
                    disabled={isLoading}
                    autoFocus={!showWelcome}
                  />
                  <button
                    type="submit"
                    disabled={!input.trim() || isLoading}
                    className="interactive-button px-3 sm:px-5 py-2 sm:py-2.5 bg-gradient-primary disabled:opacity-50 disabled:cursor-not-allowed text-white rounded-xl text-button flex items-center gap-2 group focus-smooth"
                  >
                    <Send size={16} className="group-hover:translate-x-0.5 transition-transform duration-200" />
                    <span className="hidden sm:inline text-sm">Send</span>
                  </button>
                </div>

                {/* Input Footer */}
                {!showWelcome && (
                  <div className="flex flex-col sm:flex-row items-center justify-between mt-3 px-2 gap-2 sm:gap-0">
                    <p className="text-xs sm:text-body-small text-center sm:text-left" style={{ color: 'var(--text-tertiary)' }}>
                      <span className="hidden sm:inline">Press Enter to send, Shift + Enter for new line</span>
                      <span className="sm:hidden">Tap Send or press Enter</span>
                    </p>
                    <div className="flex items-center gap-2 text-xs sm:text-body-small" style={{ color: 'var(--text-tertiary)' }}>
                      <div
                        className="w-1.5 h-1.5 rounded-full animate-pulse-soft"
                        style={{ backgroundColor: theme === 'dark' ? '#FBBF24' : '#F59E0B' }}
                      ></div>
                      <span>Demo Mode</span>
                    </div>
                  </div>
                )}
              </div>
            </form>
          </div>
        </motion.div>
      </div>
    </div>
  );
}
