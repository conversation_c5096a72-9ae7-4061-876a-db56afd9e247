'use client';

import { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import { 
  MessageSquare, 
  Plus, 
  Trash2, 
  User, 
  LogOut, 
  Menu, 
  X,
  Clock,
  Search
} from 'lucide-react';
import { useAuth } from '@/contexts/AuthContext';
import { componentClasses, cn } from '@/utils/theme';

interface ChatHistory {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count: number;
}

interface SidebarProps {
  isOpen: boolean;
  onToggle: () => void;
  onNewChat: () => void;
  onSelectChat: (chatId: string) => void;
  currentChatId: string | null;
}

export default function Sidebar({ 
  isOpen, 
  onToggle, 
  onNewChat, 
  onSelectChat, 
  currentChatId 
}: SidebarProps) {
  const { user, signOut } = useAuth();
  const [chatHistory, setChatHistory] = useState<ChatHistory[]>([]);
  const [searchQuery, setSearchQuery] = useState('');
  const [isLoading, setIsLoading] = useState(false);

  // Filter chats based on search query
  const filteredChats = chatHistory.filter(chat =>
    chat.title.toLowerCase().includes(searchQuery.toLowerCase())
  );

  // Group chats by date
  const groupChatsByDate = (chats: ChatHistory[]) => {
    const now = new Date();
    const today = new Date(now.getFullYear(), now.getMonth(), now.getDate());
    const yesterday = new Date(today.getTime() - 24 * 60 * 60 * 1000);
    const lastWeek = new Date(today.getTime() - 7 * 24 * 60 * 60 * 1000);

    const groups = {
      today: [] as ChatHistory[],
      yesterday: [] as ChatHistory[],
      lastWeek: [] as ChatHistory[],
      older: [] as ChatHistory[]
    };

    chats.forEach(chat => {
      const chatDate = new Date(chat.updated_at);
      if (chatDate >= today) {
        groups.today.push(chat);
      } else if (chatDate >= yesterday) {
        groups.yesterday.push(chat);
      } else if (chatDate >= lastWeek) {
        groups.lastWeek.push(chat);
      } else {
        groups.older.push(chat);
      }
    });

    return groups;
  };

  const groupedChats = groupChatsByDate(filteredChats);

  const formatTime = (dateString: string) => {
    const date = new Date(dateString);
    return date.toLocaleTimeString('en-US', { 
      hour: 'numeric', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const handleDeleteChat = async (chatId: string, e: React.MouseEvent) => {
    e.stopPropagation();
    // TODO: Implement delete functionality
    console.log('Delete chat:', chatId);
  };

  const handleSignOut = async () => {
    await signOut();
  };

  // Load chat history when user is authenticated
  useEffect(() => {
    if (user) {
      // TODO: Load chat history from Supabase
      // For now, using mock data
      setChatHistory([
        {
          id: '1',
          title: 'What is my zScore?',
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString(),
          message_count: 5
        },
        {
          id: '2',
          title: 'DeFi lending strategies',
          created_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          updated_at: new Date(Date.now() - 24 * 60 * 60 * 1000).toISOString(),
          message_count: 12
        }
      ]);
    }
  }, [user]);

  if (!user) {
    return null;
  }

  return (
    <>
      {/* Mobile Overlay */}
      <AnimatePresence>
        {isOpen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            onClick={onToggle}
            className="fixed inset-0 bg-black/50 backdrop-blur-sm z-40 lg:hidden"
          />
        )}
      </AnimatePresence>

      {/* Sidebar */}
      <motion.div
        initial={{ x: -320 }}
        animate={{ x: isOpen ? 0 : -320 }}
        transition={{ duration: 0.3, ease: "easeInOut" }}
        className="fixed left-0 top-0 h-full w-80 bg-surface/95 backdrop-blur-xl border-r border-border-primary z-50 flex flex-col"
      >
        {/* Header */}
        <div className="p-4 border-b border-border-primary">
          <div className="flex items-center justify-between mb-4">
            <h2 className="text-lg font-semibold font-space-grotesk" style={{ color: 'var(--text-primary)' }}>
              Chat History
            </h2>
            <button
              onClick={onToggle}
              className="p-2 rounded-lg hover:bg-surface-elevated transition-colors duration-200 lg:hidden"
            >
              <X size={20} style={{ color: 'var(--text-secondary)' }} />
            </button>
          </div>

          {/* New Chat Button */}
          <button
            onClick={onNewChat}
            className="w-full flex items-center gap-3 p-3 rounded-xl bg-gradient-primary text-white hover:shadow-brand transition-all duration-200"
          >
            <Plus size={18} />
            <span className="font-medium">New Chat</span>
          </button>

          {/* Search */}
          <div className="mt-4 relative">
            <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 text-text-tertiary" size={16} />
            <input
              type="text"
              placeholder="Search chats..."
              value={searchQuery}
              onChange={(e) => setSearchQuery(e.target.value)}
              className="w-full pl-10 pr-4 py-2 bg-surface border border-border-primary rounded-lg focus:ring-2 focus:ring-primary-500/50 focus:border-primary-500/50 transition-all duration-200 text-sm"
              style={{ color: 'var(--text-primary)' }}
            />
          </div>
        </div>

        {/* Chat List */}
        <div className="flex-1 overflow-y-auto p-4 space-y-6">
          {Object.entries(groupedChats).map(([period, chats]) => {
            if (chats.length === 0) return null;
            
            const periodLabels = {
              today: 'Today',
              yesterday: 'Yesterday',
              lastWeek: 'Last 7 days',
              older: 'Older'
            };

            return (
              <div key={period}>
                <h3 className="text-xs font-medium text-text-tertiary uppercase tracking-wider mb-2">
                  {periodLabels[period as keyof typeof periodLabels]}
                </h3>
                <div className="space-y-1">
                  {chats.map((chat) => (
                    <motion.button
                      key={chat.id}
                      onClick={() => onSelectChat(chat.id)}
                      className={cn(
                        "w-full flex items-center gap-3 p-3 rounded-lg transition-all duration-200 group text-left",
                        currentChatId === chat.id
                          ? "bg-primary-500/10 border border-primary-500/20"
                          : "hover:bg-surface-elevated"
                      )}
                      whileHover={{ scale: 1.02 }}
                      whileTap={{ scale: 0.98 }}
                    >
                      <MessageSquare 
                        size={16} 
                        className={cn(
                          "flex-shrink-0",
                          currentChatId === chat.id ? "text-primary-500" : "text-text-tertiary"
                        )}
                      />
                      <div className="flex-1 min-w-0">
                        <p className={cn(
                          "text-sm font-medium truncate",
                          currentChatId === chat.id ? "text-primary-500" : "text-text-primary"
                        )}>
                          {chat.title}
                        </p>
                        <div className="flex items-center gap-2 mt-1">
                          <Clock size={12} className="text-text-tertiary" />
                          <span className="text-xs text-text-tertiary">
                            {formatTime(chat.updated_at)}
                          </span>
                          <span className="text-xs text-text-tertiary">
                            • {chat.message_count} messages
                          </span>
                        </div>
                      </div>
                      <button
                        onClick={(e) => handleDeleteChat(chat.id, e)}
                        className="opacity-0 group-hover:opacity-100 p-1 rounded hover:bg-red-500/10 hover:text-red-500 transition-all duration-200"
                      >
                        <Trash2 size={14} />
                      </button>
                    </motion.button>
                  ))}
                </div>
              </div>
            );
          })}

          {filteredChats.length === 0 && !isLoading && (
            <div className="text-center py-8">
              <MessageSquare size={48} className="mx-auto text-text-tertiary mb-4" />
              <p className="text-text-secondary">
                {searchQuery ? 'No chats found' : 'No chat history yet'}
              </p>
              <p className="text-text-tertiary text-sm mt-1">
                {searchQuery ? 'Try a different search term' : 'Start a conversation to see your history'}
              </p>
            </div>
          )}
        </div>

        {/* User Profile */}
        <div className="p-4 border-t border-border-primary">
          <div className="flex items-center gap-3 p-3 rounded-lg bg-surface-elevated">
            <div className="w-8 h-8 bg-gradient-primary rounded-full flex items-center justify-center">
              <User size={16} className="text-white" />
            </div>
            <div className="flex-1 min-w-0">
              <p className="text-sm font-medium text-text-primary truncate">
                {user.email}
              </p>
              <p className="text-xs text-text-tertiary">
                Authenticated
              </p>
            </div>
            <button
              onClick={handleSignOut}
              className="p-2 rounded-lg hover:bg-red-500/10 hover:text-red-500 transition-all duration-200"
              title="Sign out"
            >
              <LogOut size={16} />
            </button>
          </div>
        </div>
      </motion.div>

      {/* Mobile Toggle Button */}
      <button
        onClick={onToggle}
        className="fixed top-4 left-4 z-30 p-3 bg-surface/95 backdrop-blur-xl border border-border-primary rounded-xl shadow-soft hover:shadow-medium transition-all duration-200 lg:hidden"
      >
        <Menu size={20} style={{ color: 'var(--text-primary)' }} />
      </button>
    </>
  );
}
