import { createClient, isSupabaseConfigured } from '@/utils/supabase/client';

export interface ChatMessage {
  id: string;
  content: string;
  is_user: boolean;
  created_at: string;
}

export interface Chat {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  messages?: ChatMessage[];
}

export interface ChatHistory {
  id: string;
  title: string;
  created_at: string;
  updated_at: string;
  message_count: number;
}

class ChatService {
  private supabase: any = null;
  private isAvailable: boolean = false;

  constructor() {
    if (isSupabaseConfigured()) {
      this.supabase = createClient();
      this.isAvailable = !!this.supabase;
    }
  }

  private checkAvailability() {
    if (!this.isAvailable || !this.supabase) {
      throw new Error('Supabase is not configured or available');
    }
  }

  /**
   * Create a new chat
   */
  async createChat(title: string): Promise<Chat | null> {
    try {
      this.checkAvailability();

      const { data, error } = await this.supabase
        .from('chats')
        .insert([{ title }])
        .select()
        .single();

      if (error) {
        console.error('Error creating chat:', error);
        return null;
      }

      return data;
    } catch (error) {
      console.error('Error creating chat:', error);
      return null;
    }
  }

  /**
   * Get all chats for the current user
   */
  async getUserChats(): Promise<ChatHistory[]> {
    try {
      this.checkAvailability();

      const { data, error } = await this.supabase
        .from('chats')
        .select(`
          id,
          title,
          created_at,
          updated_at,
          messages(count)
        `)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error fetching chats:', error);
        return [];
      }

      return data.map(chat => ({
        id: chat.id,
        title: chat.title,
        created_at: chat.created_at,
        updated_at: chat.updated_at,
        message_count: chat.messages?.[0]?.count || 0
      }));
    } catch (error) {
      console.error('Error fetching chats:', error);
      return [];
    }
  }

  /**
   * Get a specific chat with its messages
   */
  async getChat(chatId: string): Promise<Chat | null> {
    try {
      this.checkAvailability();

      const { data, error } = await this.supabase
        .from('chats')
        .select(`
          id,
          title,
          created_at,
          updated_at,
          messages(
            id,
            content,
            is_user,
            created_at
          )
        `)
        .eq('id', chatId)
        .single();

      if (error) {
        console.error('Error fetching chat:', error);
        return null;
      }

      return {
        ...data,
        messages: data.messages?.sort((a, b) => 
          new Date(a.created_at).getTime() - new Date(b.created_at).getTime()
        ) || []
      };
    } catch (error) {
      console.error('Error fetching chat:', error);
      return null;
    }
  }

  /**
   * Add a message to a chat
   */
  async addMessage(chatId: string, content: string, isUser: boolean): Promise<ChatMessage | null> {
    try {
      this.checkAvailability();

      const { data, error } = await this.supabase
        .from('messages')
        .insert([{
          chat_id: chatId,
          content,
          is_user: isUser
        }])
        .select()
        .single();

      if (error) {
        console.error('Error adding message:', error);
        return null;
      }

      // Update chat's updated_at timestamp
      await this.supabase
        .from('chats')
        .update({ updated_at: new Date().toISOString() })
        .eq('id', chatId);

      return data;
    } catch (error) {
      console.error('Error adding message:', error);
      return null;
    }
  }

  /**
   * Update chat title
   */
  async updateChatTitle(chatId: string, title: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('chats')
        .update({ title })
        .eq('id', chatId);

      if (error) {
        console.error('Error updating chat title:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error updating chat title:', error);
      return false;
    }
  }

  /**
   * Delete a chat and all its messages
   */
  async deleteChat(chatId: string): Promise<boolean> {
    try {
      const { error } = await this.supabase
        .from('chats')
        .delete()
        .eq('id', chatId);

      if (error) {
        console.error('Error deleting chat:', error);
        return false;
      }

      return true;
    } catch (error) {
      console.error('Error deleting chat:', error);
      return false;
    }
  }

  /**
   * Generate a title for a chat based on the first message
   */
  generateChatTitle(firstMessage: string): string {
    // Take first 50 characters and add ellipsis if longer
    const title = firstMessage.trim();
    if (title.length <= 50) {
      return title;
    }
    return title.substring(0, 47) + '...';
  }

  /**
   * Search chats by title
   */
  async searchChats(query: string): Promise<ChatHistory[]> {
    try {
      const { data, error } = await this.supabase
        .from('chats')
        .select(`
          id,
          title,
          created_at,
          updated_at,
          messages(count)
        `)
        .ilike('title', `%${query}%`)
        .order('updated_at', { ascending: false });

      if (error) {
        console.error('Error searching chats:', error);
        return [];
      }

      return data.map(chat => ({
        id: chat.id,
        title: chat.title,
        created_at: chat.created_at,
        updated_at: chat.updated_at,
        message_count: chat.messages?.[0]?.count || 0
      }));
    } catch (error) {
      console.error('Error searching chats:', error);
      return [];
    }
  }
}

export const chatService = new ChatService();
