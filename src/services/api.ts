/**
 * API Service Module
 * Handles all API calls with proper error handling and type definitions
 */

export interface QueryRequest {
  prompt: string;
}

export interface QueryResponse {
  result: string;
}

export interface ApiError {
  message: string;
  status?: number;
}

/**
 * Custom error class for API-related errors
 */
export class ApiServiceError extends Error {
  public status?: number;

  constructor(message: string, status?: number) {
    super(message);
    this.name = 'ApiServiceError';
    this.status = status;
  }
}

/**
 * Service class to handle API calls
 */
export class ApiService {
  private static readonly BASE_URL = '/api';
  private static readonly DEFAULT_TIMEOUT = 30000; // 30 seconds

  /**
   * Send a query to the AI service
   * @param prompt - The user's prompt/question
   * @returns Promise<QueryResponse> - The AI response
   * @throws ApiServiceError - When the API call fails
   */
  static async sendQuery(prompt: string): Promise<QueryResponse> {
    if (!prompt.trim()) {
      throw new ApiServiceError('Prompt cannot be empty');
    }

    const controller = new AbortController();
    const timeoutId = setTimeout(() => controller.abort(), this.DEFAULT_TIMEOUT);

    try {
      const response = await fetch(`${this.BASE_URL}/query`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({ prompt: prompt.trim() }),
        signal: controller.signal,
      });

      clearTimeout(timeoutId);

      if (!response.ok) {
        const errorText = await response.text().catch(() => 'Unknown error');
        throw new ApiServiceError(
          `API request failed: ${response.status} ${response.statusText}. ${errorText}`,
          response.status
        );
      }

      const data: QueryResponse = await response.json();
      
      if (!data.result) {
        throw new ApiServiceError('Invalid response format: missing result field');
      }

      return data;
    } catch (error) {
      clearTimeout(timeoutId);

      if (error instanceof ApiServiceError) {
        throw error;
      }

      if (error instanceof Error) {
        if (error.name === 'AbortError') {
          throw new ApiServiceError('Request timeout - please try again');
        }
        
        if (error.message.includes('Failed to fetch')) {
          throw new ApiServiceError('Network error - please check your connection');
        }

        throw new ApiServiceError(`Request failed: ${error.message}`);
      }

      throw new ApiServiceError('An unexpected error occurred');
    }
  }

  /**
   * Check if the API service is available
   * @returns Promise<boolean> - True if the service is available
   */
  static async healthCheck(): Promise<boolean> {
    try {
      const response = await fetch(`${this.BASE_URL}/health`, {
        method: 'GET',
        headers: {
          'Content-Type': 'application/json',
        },
      });
      return response.ok;
    } catch {
      return false;
    }
  }
}

/**
 * Convenience function for sending queries
 * @param prompt - The user's prompt/question
 * @returns Promise<QueryResponse> - The AI response
 */
export const sendQuery = (prompt: string): Promise<QueryResponse> => {
  return ApiService.sendQuery(prompt);
};

/**
 * Convenience function for health checks
 * @returns Promise<boolean> - True if the service is available
 */
export const checkApiHealth = (): Promise<boolean> => {
  return ApiService.healthCheck();
};
