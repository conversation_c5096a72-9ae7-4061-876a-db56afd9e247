/**
 * Environment Configuration Module
 * Handles environment-specific settings in a modular way
 */

export interface EnvironmentConfig {
  nodeEnv: string;
  isDevelopment: boolean;
  isProduction: boolean;
  api: {
    externalUrl: string;
    endpoint: string;
    timeout: number;
    fullUrl: string;
  };
  app: {
    name: string;
    version: string;
  };
  debug: {
    enabled: boolean;
    logLevel: string;
  };
}

/**
 * Get environment variable with optional default value
 */
function getEnvVar(key: string, defaultValue?: string): string {
  const value = process.env[key];
  if (value === undefined) {
    if (defaultValue !== undefined) {
      return defaultValue;
    }
    throw new Error(`Environment variable ${key} is required but not set`);
  }
  return value;
}

/**
 * Get boolean environment variable
 */
function getBooleanEnvVar(key: string, defaultValue: boolean = false): boolean {
  const value = process.env[key];
  if (value === undefined) {
    return defaultValue;
  }
  return value.toLowerCase() === 'true';
}

/**
 * Get number environment variable
 */
function getNumberEnvVar(key: string, defaultValue: number): number {
  const value = process.env[key];
  if (value === undefined) {
    return defaultValue;
  }
  const parsed = parseInt(value, 10);
  if (isNaN(parsed)) {
    throw new Error(`Environment variable ${key} must be a valid number, got: ${value}`);
  }
  return parsed;
}

/**
 * Create environment configuration
 */
function createEnvironmentConfig(): EnvironmentConfig {
  const nodeEnv = getEnvVar('NODE_ENV', 'development');
  const isDevelopment = nodeEnv === 'development';
  const isProduction = nodeEnv === 'production';

  // API Configuration
  const externalUrl = getEnvVar(
    'EXTERNAL_API_URL',
    isDevelopment ? 'http://52.66.192.54:8001' : 'https://agentapi.myzscore.ai'
  );
  const endpoint = getEnvVar('EXTERNAL_API_ENDPOINT', '/query');
  const timeout = getNumberEnvVar('EXTERNAL_API_TIMEOUT', 30000);

  // App Configuration
  const appName = getEnvVar('APP_NAME', 'zScore Agent');
  const appVersion = getEnvVar('APP_VERSION', '1.0.0');

  // Debug Configuration
  const debugEnabled = getBooleanEnvVar('DEBUG_MODE', isDevelopment);
  const logLevel = getEnvVar('LOG_LEVEL', isDevelopment ? 'debug' : 'error');

  return {
    nodeEnv,
    isDevelopment,
    isProduction,
    api: {
      externalUrl,
      endpoint,
      timeout,
      fullUrl: `${externalUrl}${endpoint}`,
    },
    app: {
      name: appName,
      version: appVersion,
    },
    debug: {
      enabled: debugEnabled,
      logLevel,
    },
  };
}

/**
 * Environment configuration instance
 */
export const env: EnvironmentConfig = createEnvironmentConfig();

/**
 * Validate environment configuration
 */
export function validateEnvironment(): void {
  const requiredVars = [
    'EXTERNAL_API_URL',
  ];

  const missing = requiredVars.filter(key => !process.env[key]);
  
  if (missing.length > 0) {
    throw new Error(`Missing required environment variables: ${missing.join(', ')}`);
  }

  // Validate URL format
  try {
    new URL(env.api.externalUrl);
  } catch {
    throw new Error(`Invalid EXTERNAL_API_URL format: ${env.api.externalUrl}`);
  }

  // Validate timeout
  if (env.api.timeout <= 0) {
    throw new Error(`EXTERNAL_API_TIMEOUT must be a positive number, got: ${env.api.timeout}`);
  }
}

/**
 * Log environment configuration (safe for production)
 */
export function logEnvironmentInfo(): void {
  if (env.debug.enabled) {
    console.log('Environment Configuration:', {
      nodeEnv: env.nodeEnv,
      isDevelopment: env.isDevelopment,
      isProduction: env.isProduction,
      api: {
        externalUrl: env.api.externalUrl,
        endpoint: env.api.endpoint,
        timeout: env.api.timeout,
      },
      app: env.app,
      debug: env.debug,
    });
  }
}

// Validate environment on module load
try {
  validateEnvironment();
  logEnvironmentInfo();
} catch (error) {
  console.error('Environment configuration error:', error);
  if (env.isProduction) {
    process.exit(1);
  }
}
