'use client';

import { createContext, useContext, useEffect, useState } from 'react';
import { User } from '@supabase/supabase-js';
import { createClient, isSupabaseConfigured } from '@/utils/supabase/client';

interface AuthContextType {
  user: User | null;
  loading: boolean;
  isSupabaseAvailable: boolean;
  signOut: () => Promise<void>;
}

const AuthContext = createContext<AuthContextType | undefined>(undefined);

export function AuthProvider({ children }: { children: React.ReactNode }) {
  const [user, setUser] = useState<User | null>(null);
  const [loading, setLoading] = useState(true);
  const [supabaseAvailable, setSupabaseAvailable] = useState(false);
  const [supabase, setSupabase] = useState<any>(null);

  useEffect(() => {
    const initializeAuth = async () => {
      // Check if Supabase is configured
      if (!isSupabaseConfigured()) {
        console.log('Supabase not configured - running in temporary chat mode');
        setSupabaseAvailable(false);
        setLoading(false);
        return;
      }

      // Try to create Supabase client
      const client = createClient();
      if (!client) {
        console.log('Failed to create Supabase client - running in temporary chat mode');
        setSupabaseAvailable(false);
        setLoading(false);
        return;
      }

      setSupabase(client);
      setSupabaseAvailable(true);

      try {
        // Get initial session
        const { data: { session } } = await client.auth.getSession();
        setUser(session?.user ?? null);
        setLoading(false);

        // Listen for auth changes
        const { data: { subscription } } = client.auth.onAuthStateChange(
          async (event, session) => {
            setUser(session?.user ?? null);
            setLoading(false);
          }
        );

        return () => subscription.unsubscribe();
      } catch (error) {
        console.error('Error initializing auth:', error);
        setSupabaseAvailable(false);
        setLoading(false);
      }
    };

    initializeAuth();
  }, []);

  const signOut = async () => {
    if (supabase) {
      try {
        await supabase.auth.signOut();
      } catch (error) {
        console.error('Error signing out:', error);
      }
    }
  };

  const value = {
    user,
    loading,
    isSupabaseAvailable: supabaseAvailable,
    signOut,
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
}

export function useAuth() {
  const context = useContext(AuthContext);
  if (context === undefined) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
}
