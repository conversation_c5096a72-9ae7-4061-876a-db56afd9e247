# 🚀 Setup Guide - zScore Agent

## Quick Start (Demo Mode)

The application will now run in **demo mode** without Supabase setup. You can test all features except authentication and chat history persistence.

### 1. Run the Application
```bash
npm run dev
```

### 2. Open Browser
Navigate to [http://localhost:3000](http://localhost:3000)

### 3. Test Features
- ✅ Chat interface works
- ✅ Theme toggle works  
- ✅ Question suggestions work
- ❌ Login/authentication disabled (demo mode)
- ❌ Chat history not saved

---

## Full Setup (With Authentication & Chat History)

To enable all features including authentication and persistent chat history, you need to set up Supabase.

### Step 1: Create Supabase Project

1. Go to [supabase.com](https://supabase.com)
2. Sign up/login and create a new project
3. Wait for the project to be ready (2-3 minutes)

### Step 2: Get Your Credentials

1. In your Supabase dashboard, go to **Settings** → **API**
2. Copy these values:
   - **Project URL** (looks like: `https://abcdefgh.supabase.co`)
   - **Anon public key** (starts with `eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...`)

### Step 3: Update Environment Variables

1. Open `.env.development` in your project
2. Replace the placeholder values:

```env
# Replace these with your actual Supabase credentials
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...
NEXT_PUBLIC_SITE_URL=http://localhost:3000
```

### Step 4: Set Up Database (Migration)

**What is a Migration?**
A migration is a script that sets up your database structure (tables, columns, relationships, security rules). Think of it as a blueprint for your database.

**Why do we need it?**
- Creates tables for users, chats, and messages
- Sets up security rules so users can only see their own data
- Creates indexes for better performance
- Sets up automatic functions (like creating user profiles)

**How to run the migration:**

1. In your Supabase dashboard, go to **SQL Editor**
2. Click **New Query**
3. Copy the entire content from `supabase/migrations/001_chat_schema.sql`
4. Paste it into the SQL editor
5. Click **Run** button

### Step 5: Configure Google OAuth (Optional)

1. In Supabase dashboard, go to **Authentication** → **Providers**
2. Enable **Google** provider
3. Add your Google OAuth credentials (requires Google Cloud Console setup)

### Step 6: Restart Your Application

```bash
# Stop the dev server (Ctrl+C)
# Then restart
npm run dev
```

---

## 🎯 What Each Feature Does

### Without Supabase (Demo Mode)
- **Chat Interface**: Works with mock responses
- **Theme Toggle**: Light/dark mode switching
- **Question Suggestions**: Expandable dropdown with suggested questions
- **Responsive Design**: Mobile-friendly interface

### With Supabase (Full Mode)
- **User Authentication**: Email/password and Google login
- **Chat History**: All conversations saved and retrievable
- **User Profiles**: Personal user data management
- **Sidebar Navigation**: ChatGPT-style chat history browser
- **Search**: Find previous conversations
- **Data Security**: Each user sees only their own data

---

## 🔧 Troubleshooting

### Error: "Invalid URL"
- Check that your Supabase URL is correct and starts with `https://`
- Make sure you've replaced the placeholder values in `.env.development`

### Error: "Invalid JWT"
- Check that your Supabase anon key is correct
- Make sure the key is the **anon public** key, not the service role key

### Login not working
- Verify your Supabase project is active
- Check that the database migration was run successfully
- Ensure your site URL is configured correctly

### Chat history not saving
- Verify the database migration was run
- Check browser console for any errors
- Make sure you're logged in

---

## 🎨 Customization

### Changing the AI API
Edit `src/services/api.ts` to point to your preferred AI service.

### Modifying the Design
The design system is in `src/app/globals.css` and `src/utils/theme.ts`.

### Adding New Features
The modular architecture makes it easy to add new components and features.

---

## 📞 Need Help?

1. Check the browser console for error messages
2. Verify all environment variables are set correctly
3. Make sure the Supabase migration was run successfully
4. Test in an incognito window to rule out browser cache issues

The application is designed to work in demo mode even without Supabase, so you can always test the core functionality first!
