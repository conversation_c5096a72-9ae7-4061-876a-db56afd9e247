# zScore Agent - AI Chat Interface

A modern, responsive AI chat interface built with Next.js, featuring authentication, chat history, and a beautiful design system.

## ✨ Features

- **🎨 Modern Design**: Clean, responsive interface with light/dark theme support
- **🔐 Authentication**: Email and Google OAuth login via Supabase
- **💬 Chat Interface**: Real-time chat with AI assistant
- **📚 Chat History**: Persistent chat storage for authenticated users
- **🔍 Smart Suggestions**: Expandable dropdown with suggested questions
- **📱 Mobile Responsive**: Optimized for all device sizes
- **🎯 Clean Architecture**: Modular, maintainable codebase

## 🚀 Quick Start

### Prerequisites

- Node.js 18+
- npm or yarn
- Supabase account (for authentication and chat history)

### Installation

1. **Install dependencies**
   ```bash
   npm install
   ```

2. **Set up environment variables**
   ```bash
   cp .env.example .env.development
   ```

   Update `.env.development` with your actual values:
   ```env
   # Supabase Configuration
   NEXT_PUBLIC_SUPABASE_URL=your_supabase_project_url
   NEXT_PUBLIC_SUPABASE_ANON_KEY=your_supabase_anon_key
   NEXT_PUBLIC_SITE_URL=http://localhost:3000
   ```

3. **Set up Supabase Database**
   - Create a new Supabase project
   - Run the SQL migration in `supabase/migrations/001_chat_schema.sql`
   - Configure Google OAuth in Supabase Auth settings (optional)

4. **Run the development server**
   ```bash
   npm run dev
   ```

5. **Open your browser**
   Navigate to [http://localhost:3000](http://localhost:3000)

## 🏗️ Architecture

### Key Components

- **ChatInterface**: Main chat component with message handling
- **Sidebar**: Chat history navigation (authenticated users only)
- **AuthContext**: Authentication state management
- **ChatService**: Database operations for chat history
- **ThemeProvider**: Dynamic theme system

### Features Toggle

- **Guest Mode**: Users can chat without authentication (no history saved)
- **Authenticated Mode**: Full features with chat history and user profile

## 🔧 Configuration

### Environment Variables

| Variable | Description | Required |
|----------|-------------|----------|
| `NEXT_PUBLIC_SUPABASE_URL` | Supabase project URL | Yes |
| `NEXT_PUBLIC_SUPABASE_ANON_KEY` | Supabase anonymous key | Yes |
| `NEXT_PUBLIC_SITE_URL` | Site URL for OAuth redirects | Yes |
| `EXTERNAL_API_URL` | External AI API endpoint | Yes |

## 🚀 Deployment

The easiest way to deploy your Next.js app is to use the [Vercel Platform](https://vercel.com/new?utm_medium=default-template&filter=next.js&utm_source=create-next-app&utm_campaign=create-next-app-readme) from the creators of Next.js.

Built with ❤️ using Next.js, Supabase, and modern web technologies.
